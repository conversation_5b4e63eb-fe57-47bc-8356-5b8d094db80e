import 'dart:async';
import 'dart:isolate';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import 'package:web_socket_client/web_socket_client.dart';
import 'package:x1440/api/auth_interceptor.dart';
import 'package:x1440/repositories/models/shim_websocket_message.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart';
import 'package:x1440/ui/blocs/messaging/messaging_bloc.dart';
import 'package:x1440/ui/blocs/messaging/messaging_event.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/utils/json_utils.dart';

import 'models/websocket_connection.dart';
import 'models/websocket_params.dart';

const Duration _websocketTimeoutOverrideDuration = Duration(seconds: 60);

class ShimWebsocketRepositoryImpl extends ShimWebsocketRepository {
  final RemoteLogger _logger;
  final LocalStorageRepository _localStorage;

  ShimWebsocketRepositoryImpl(this._logger, this._localStorage);

  WebSocket? _socket;

  final StreamController<ShimWebsocketMessage> _messagesStreamController =
      StreamController<ShimWebsocketMessage>.broadcast();
  final StreamController<WebsocketConnection>
      _websocketConnectionStateStreamController =
      StreamController<WebsocketConnection>.broadcast();
  WebsocketConnection _connectionState = const WebsocketConnection();

  @override
  Stream<ShimWebsocketMessage> get messagesStream =>
      _messagesStreamController.stream;

  @override
  Stream<WebsocketConnection> get websocketConnectionStateStream =>
      _websocketConnectionStateStreamController.stream;

  @override
  Future<void> init() async {
    websocketConnectionStateStream.listen(_webSocketConnectionStateListener);
  }

  @override
  Future<bool> connect() async {
    // HYPOTHESIS 3: Add logging for websocket connection attempts
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: connect() called');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Current connection state: ${_connectionState.connectionState}');
    final result = await _connectToWebsocket();
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: _connectToWebsocket() returned: $result');
    return result;
  }

  @override
  Future<void> disconnect() async {
    _logger.info('disconnecting from websocket');
    _socket?.close();
    _socket = null;
  }

  /// Force refresh credentials and retry connection - useful for debugging auth issues
  Future<bool> connectWithFreshCredentials() async {
    _logger.info('🔄 WEBSOCKET_CONNECT_DEBUG: connectWithFreshCredentials() called - forcing credential refresh');

    // Wait a moment for any ongoing auth operations to complete
    await Future.delayed(const Duration(milliseconds: 500));

    return _connectToWebsocket();
  }

  int connectAttempts = 0;
  Future<bool> _connectToWebsocket() async {
    // HYPOTHESIS 3: Add comprehensive logging for websocket connection process
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: _connectToWebsocket() started');

    WebsocketParams? params = await _getParamsFromLocalStorage();
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Got params from storage: ${params != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Params URI: "${params?.uri}"');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Params headers: ${params?.headers}');

    if (_connectionState.connectionState.isConnected ||
        _connectionState.connectionState.isConnecting) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Already connected/connecting, returning true');
      return true;
    }

    if (params?.uri == null) {
      _logger.warn('🔍 WEBSOCKET_CONNECT_DEBUG: No URI available, cannot connect');
      return false;
    }

    _logger.info(
        'connectToWebsocket attempt: ${connectAttempts++} with connectionState: ${_connectionState.connectionState}; X-Correlation-Id: ${params?.headers?['X-Correlation-Id']}');

    Isolate currentIsolate = Isolate.current;

    params = params?.copyWith(
      headers: {
        ...?params.headers,
        'X-Connection-Id': const Uuid().v4(),
        'X-Isolate-Name': currentIsolate.debugName,
      },
    );

    setupWebsocketTimeoutOverride();

    _logger.info('closing existing socket and connecting with: ${params?.uri}');
    _socket?.close();
    _socket = null;
    _socket = _connectToWebSocketFromParams(params!);
    _socket?.connection.listen((event) {
      try {
      _logger.info('socket connection event: ${event.runtimeType}');
      _connectionState = WebsocketConnection.fromConnection(event);
      _logger.info('socket connection state: ${_connectionState.connectionState}');
      _websocketConnectionStateStreamController.add(_connectionState);

      if (_connectionState.connectionState.isConnected == true) {
        _websocketTimeoutOverride?.cancel();
        _hasRetriedAfterTimeout = false;
      }

      if (event is Disconnected) {
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: DISCONNECTED EVENT HANDLER CALLED!');
        _logger.info(
            'socket disconnected reason: ${event.reason}; error: ${event
                .error}; code: ${event.code}');

        // COMPREHENSIVE ERROR ANALYSIS: Log everything about the error
        final errorString = event.error?.toString() ?? 'null';
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: Error string: "$errorString"');
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: Contains "was not upgraded to websocket": ${errorString.contains('was not upgraded to websocket')}');
        _logger.info('🚨🚨🚨 WEBSOCKET_DEBUG: Contains "HTTP status code: 401": ${errorString.contains('HTTP status code: 401')}');

        // TODO: can we better match this? It seems to be standard text with a null error code . . . ideally we'd have an error code to match for this?
        if (errorString.contains('was not upgraded to websocket')) {
          _logger.info(
              'socket disconnected due to not being upgraded to websocket');

          // Check if this is a 401 authentication error and trigger auto-login
          if (errorString.contains('HTTP status code: 401') ||
              errorString.contains('status code: 401')) {
            _logger.info('🚨 WEBSOCKET_401_DETECTED: Triggering auto-login for websocket 401 error');
            _logger.info('🚨 WEBSOCKET_401_DETECTED: Current credentials before auto-login attempt');

            // Log current credential state before triggering auto-login
            _localStorage.getCredentials().then((creds) {
              _logger.info('🚨 WEBSOCKET_401_DETECTED: WebSocket URL: ${creds.webSocketUrl != null ? "present" : "missing"}');
              _logger.info('🚨 WEBSOCKET_401_DETECTED: Session token: ${creds.sessionToken != null ? "present" : "missing"}');
              _logger.info('🚨 WEBSOCKET_401_DETECTED: Auth token: ${creds.authorizationToken != null ? "present" : "missing"}');
            });

            GetIt.I<AuthBloc>().add(AttemptAutoLoginEvent(force: true));

            // Schedule a retry after auto-login completes
            Future.delayed(const Duration(seconds: 3), () async {
              _logger.info('🔄 WEBSOCKET_401_DETECTED: Attempting reconnection after auto-login delay');
              final newCreds = await _localStorage.getCredentials();
              _logger.info('🔄 WEBSOCKET_401_DETECTED: Post-login WebSocket URL: ${newCreds.webSocketUrl != null ? "present" : "missing"}');
              _logger.info('🔄 WEBSOCKET_401_DETECTED: Post-login Session token: ${newCreds.sessionToken != null ? "present" : "missing"}');
              _logger.info('🔄 WEBSOCKET_401_DETECTED: Post-login Auth token: ${newCreds.authorizationToken != null ? "present" : "missing"}');

              if (newCreds.webSocketUrl != null && newCreds.sessionToken != null && newCreds.authorizationToken != null) {
                _logger.info('🔄 WEBSOCKET_401_DETECTED: Credentials look good, attempting reconnection');
                await connectWithFreshCredentials();
              } else {
                _logger.warn('🚨 WEBSOCKET_401_DETECTED: Credentials still incomplete after auto-login');
              }
            });
          } else {
            _logger.info('⚠️ WEBSOCKET_NON_401: Non-401 websocket upgrade failure');
          }
        } else if (event.code != null) {
          _logger.info(
              'socket disconnected with code: ${event
                  .code}; attempting to reconnect');
          _handleWebsocketDisconnect();
        } else {
          // HYPOTHESIS 1: Log all other error types
          _logger.info('🔍 WEBSOCKET_DEBUG: Non-upgrade error: "${event.error.toString()}"');
        }
      }
    } catch (e) {
      _logger.warn('socket connection error: $e');
    }
    }, onError: (error) {
      _logger.warn('socket error: $error');
      // _websocketConnectionStateStream.add(WebsocketConnection.fromError(error));
    }, onDone: () {
      _logger.info('socket done');
      _socket?.close();
      _socket = null;
      // _websocketConnectionStateStream.add(WebsocketConnection.fromDone());
    });

    _socket?.messages.listen((event) {
      Map<String, dynamic> eventJson = (event is Map<String, dynamic>)
          ? event
          : () {
              try {
                return safeJsonDecode(event);
              } catch (e) {
                _logger.warn('error parsing message: $e');
                return {
                  'message': 'error parsing message',
                  'error': e.toString(),
                  'raw': event
                };
              }
            }();

      _messagesStreamController.add(ShimWebsocketMessage.fromJson(eventJson));
    });

    _logger.info('finishing connecting with: ${_socket?.connection.state}');
    final isConnected = _connectionState.connectionState.isConnected == true;
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: _connectToWebsocket() returned: $isConnected');
    return isConnected;
  }

  Future<WebsocketParams?> _getParamsFromLocalStorage() async {
    final credentials = await _localStorage.getCredentials();

    String? websocketUrl = credentials.webSocketUrl;

    // ENHANCED LOGGING: Log all credential states for debugging
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Getting credentials from storage');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: WebSocket URL present: ${websocketUrl != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Session token present: ${credentials.sessionToken != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Authorization token present: ${credentials.authorizationToken != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Access token present: ${credentials.accessToken != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: User ID present: ${credentials.userId != null}');
    _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Instance URL present: ${credentials.instanceUrl != null}');

    if (websocketUrl != null) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: WebSocket URL: "$websocketUrl"');
    }

    if (credentials.sessionToken != null) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Session token (first 20 chars): "${credentials.sessionToken!.substring(0, credentials.sessionToken!.length > 20 ? 20 : credentials.sessionToken!.length)}..."');
    }

    if (credentials.authorizationToken != null) {
      _logger.info('🔍 WEBSOCKET_CONNECT_DEBUG: Authorization token (first 20 chars): "${credentials.authorizationToken!.substring(0, credentials.authorizationToken!.length > 20 ? 20 : credentials.authorizationToken!.length)}..."');
    }

    if (websocketUrl == null ||
        credentials.sessionToken == null ||
        credentials.authorizationToken == null) {
      _logger.warn('🚨 WEBSOCKET_CONNECT_DEBUG: Missing required credentials for WebSocket connection');
      return null;
    }

    Map<String, dynamic> headers = {
      "Authorization": "Bearer ${credentials.authorizationToken}",
      sessionTokenHeaderKey: credentials.sessionToken,
      "X-Correlation-Id": const Uuid().v4(),
    };

// Initially wait 1s and double the wait time until a maximum step of of 3 is reached.
// [1, 2, 4, 16, 32, 64]
    final backoff = BinaryExponentialBackoff(
        initial: const Duration(seconds: 1), maximumStep: 6);

    return WebsocketParams(
        uri: Uri.parse(websocketUrl),
        headers: headers,
        backoff: backoff,
        pingInterval: const Duration(seconds: 5));
  }

  bool _hasRetriedAfterTimeout = false;
  Timer? _websocketTimeoutOverride;
  void setupWebsocketTimeoutOverride() {
    _websocketTimeoutOverride?.cancel();
    _websocketTimeoutOverride =
        Timer(_websocketTimeoutOverrideDuration, () async {
      _logger.warn(
          'websocket connection HARD timed out, current connection state: $_connectionState');
      if (_connectionState.connectionState.isConnected == true) {
        return;
      } else if (_connectionState.connectionState !=
          WebsocketConnectionState.disconnected) {
        _logger.warn('disconnecting from websocket due to timeout');
        await disconnect();
      }
      if (!_hasRetriedAfterTimeout) {
        _hasRetriedAfterTimeout = true;
        await Future.delayed(const Duration(seconds: 5));
        _logger.warn('reconnecting to MessagingBloc after timeout');
        GetIt.I<MessagingBloc>().add(ConnectEvent());
      } else {
        _logger.warn('attempting to auto-login after timeout');
        GetIt.I<AuthBloc>().add(AttemptAutoLoginEvent(force: true));
      }
    });
  }

  WebSocket _connectToWebSocketFromParams(WebsocketParams params) =>
      WebSocket(params.uri!,
          headers: params.headers,
          protocols: params.protocols,
          pingInterval: params.pingInterval,
          backoff: params.backoff,
          timeout: params.timeout,
          binaryType: params.binaryType);

  Future<void> _handleWebsocketDisconnect() async {
    _logger.info('handleWebsocketDisconnect');
    _socket?.close();
    _socket = null;
  }

  void _webSocketConnectionStateListener(WebsocketConnection connection) {
    _logger.info('setWebSocketConnectionState: $connection');
    _connectionState = connection;
  }

  /// Debug method to test WebSocket connection with current credentials
  Future<void> debugTestConnection() async {
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Starting manual connection test');

    final credentials = await _localStorage.getCredentials();
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Current credentials state:');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - WebSocket URL: ${credentials.webSocketUrl}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Session token present: ${credentials.sessionToken != null}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Auth token present: ${credentials.authorizationToken != null}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Access token present: ${credentials.accessToken != null}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - User ID: ${credentials.userId}');
    _logger.info('🧪 WEBSOCKET_DEBUG_TEST: - Instance URL: ${credentials.instanceUrl}');

    if (credentials.webSocketUrl != null &&
        credentials.sessionToken != null &&
        credentials.authorizationToken != null) {
      _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Credentials look complete, attempting connection');
      final result = await connect();
      _logger.info('🧪 WEBSOCKET_DEBUG_TEST: Connection result: $result');
    } else {
      _logger.warn('🧪 WEBSOCKET_DEBUG_TEST: Incomplete credentials, cannot test connection');
    }
  }
}
